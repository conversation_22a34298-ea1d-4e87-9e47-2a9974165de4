import React from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Checkbox from "@mui/material/Checkbox";
import TableSortLabel from "@mui/material/TableSortLabel";
import IconButton from "@mui/material/IconButton";
import Box from "@mui/material/Box";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";

import { MoreVertIcon, UnfoldMoreIcon, DragIndicatorIcon } from "@/components/common/icons";

// MUI TableSortLabel expects an icon component that accepts a className prop
const InactiveSortIcon: React.FC<{ className?: string }> = ({ className }) => (
  <UnfoldMoreIcon className={className} />
);


export type Order = "asc" | "desc";

export type Column<Row> = {
  id: keyof Row | string;
  label: string;
  width?: number | string;
  align?: "left" | "right" | "center" | "inherit" | "justify";
  sortable?: boolean;
  getValue?: (row: Row) => React.ReactNode;
  renderCell?: (row: Row) => React.ReactNode;
  getSortValue?: (row: Row) => string | number;
  sticky?: "left" | "right";
};

export type BaseDataTableProps<Row extends { id: string }> = {
  columns: Column<Row>[];
  rows: Row[];
  loading?: boolean;
  orderBy?: string;
  order?: Order;
  onSortChange?: (orderBy: string, order: Order) => void;
  selected?: string[];
  onSelectionChange?: (ids: string[]) => void;
  getRowHighlight?: (row: Row) => "issue" | undefined;
  onActionClick?: (row: Row, action: string) => void;
  height?: number | string;
};

function stableSort<Row>(array: Row[], comparator: (a: Row, b: Row) => number) {
  const stabilized = array.map((el, index) => [el, index] as const);
  stabilized.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });
  return stabilized.map((el) => el[0]);
}

export function BaseDataTable<Row extends { id: string }>(props: BaseDataTableProps<Row>) {
  const { columns, rows, orderBy, order = "asc", onSortChange, selected, onSelectionChange, getRowHighlight, onActionClick, height = 520 } = props;

  const allIds = React.useMemo(() => rows.map((r) => r.id), [rows]);
  const selectedSet = React.useMemo(() => new Set(selected ?? []), [selected]);
  const allSelected = selectedSet.size > 0 && allIds.every((id) => selectedSet.has(id));
  const someSelected = selectedSet.size > 0 && !allSelected;

  const handleToggleAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!onSelectionChange) return;
    if (e.target.checked) onSelectionChange(allIds);
    else onSelectionChange([]);
  };
  const handleToggleOne = (id: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!onSelectionChange) return;
    const next = new Set(selectedSet);
    if (e.target.checked) next.add(id);
    else next.delete(id);
    onSelectionChange(Array.from(next));
  };

  const normalize = (v: unknown): string | number => {
    if (typeof v === "number") return v;
    if (typeof v === "string") {
      const n = Number(v);
      return Number.isNaN(n) ? v : n;
    }
    const n = Number(v as any);
    return Number.isNaN(n) ? String(v ?? "") : n;
  };

  const getComparator = React.useCallback(
    (orderByKey?: string, orderDir: Order = "asc") =>
      (a: Row, b: Row) => {
        if (!orderByKey) return 0;
        const col = columns.find((c) => String(c.id) === orderByKey);
        const va = col?.getSortValue ? col.getSortValue(a) : normalize((a as Record<string, unknown>)[orderByKey]);
        const vb = col?.getSortValue ? col.getSortValue(b) : normalize((b as Record<string, unknown>)[orderByKey]);
        const cmp = va < vb ? -1 : va > vb ? 1 : 0;
        return orderDir === "asc" ? cmp : -cmp;
      },
    [columns]
  );

  const sortedRows = React.useMemo(() => stableSort(rows, getComparator(orderBy, order)), [rows, orderBy, order, getComparator]);

  const [menuRowId, setMenuRowId] = React.useState<string | null>(null);
  const [menuAnchor, setMenuAnchor] = React.useState<HTMLElement | null>(null);
  const openMenu = (rowId: string) => (e: React.MouseEvent<HTMLElement>) => {
    setMenuRowId(rowId);
    setMenuAnchor(e.currentTarget);
  };
  const closeMenu = () => {
    setMenuAnchor(null);
    setMenuRowId(null);
  };

  const onSortToggle = (colId: string, sortable?: boolean) => () => {
    if (!sortable) return;
    if (!onSortChange) return;
    if (orderBy !== colId) onSortChange(colId, "asc");
    else onSortChange(colId, order === "asc" ? "desc" : "asc");
  };

  return (
    <Paper variant="outlined" sx={{ borderRadius: 2, overflow: "hidden" }}>
      <TableContainer sx={{ maxHeight: typeof height === "number" ? `${height}px` : height }}>
        <Table stickyHeader size="small" aria-label="data table">
          <TableHead>
            <TableRow>
              {/* Select column */}
              <TableCell
                padding="checkbox"
                sx={{ position: "sticky", left: 0, zIndex: 2, bgcolor: "background.paper" }}
              >
                <Checkbox
                  indeterminate={someSelected}
                  checked={allSelected}
                  onChange={handleToggleAll}
                  slotProps={{ input: { "aria-label": "select all" } }}
                />
              </TableCell>
              {columns.map((col) => {
                const active = orderBy === String(col.id);
                return (
                  <TableCell
                    key={String(col.id)}
                    align={col.align}
                    sortDirection={active ? order : false}
                    sx={{
                      ...(col.sticky === "left" && { position: "sticky", left: 48, zIndex: 1, bgcolor: "background.paper" }),
                      ...(col.sticky === "right" && { position: "sticky", right: 48, zIndex: 1, bgcolor: "background.paper" }),
                      width: col.width,
                      whiteSpace: "nowrap",
                    }}
                  >
                    {col.sortable ? (
                      <TableSortLabel
                        active={active}
                        direction={active ? order : "asc"}
                        onClick={onSortToggle(String(col.id), col.sortable)}
                        IconComponent={active ? undefined : InactiveSortIcon}
                      >
                        {col.label}
                      </TableSortLabel>
                    ) : (
                      col.label
                    )}
                  </TableCell>
                );
              })}
              {/* Actions header placeholder */}
              <TableCell
                padding="checkbox"
                sx={{ position: "sticky", right: 0, zIndex: 2, bgcolor: "background.paper" }}
              />
            </TableRow>
          </TableHead>
          <TableBody>
            {sortedRows.map((row) => {
              const highlight = getRowHighlight?.(row);
              const isSelected = selectedSet.has(row.id);
              return (
                <TableRow
                  key={row.id}
                  hover
                  selected={false}
                  sx={(theme) => ({
                    bgcolor: highlight === "issue" ? theme.palette.customLightOrange.main + "14" : undefined,
                  })}
                >
                  <TableCell padding="checkbox" sx={{ position: "sticky", left: 0, zIndex: 1, bgcolor: "background.paper" }}>
                    <Checkbox checked={isSelected} onChange={handleToggleOne(row.id)} slotProps={{ input: { "aria-label": `select ${row.id}` } }} />
                  </TableCell>
                  {columns.map((col) => (
                    <TableCell
                      key={String(col.id)}
                      align={col.align}
                      sx={{
                        ...(col.sticky === "left" && { position: "sticky", left: 48, zIndex: 1, bgcolor: "background.paper" }),
                        ...(col.sticky === "right" && { position: "sticky", right: 48, zIndex: 1, bgcolor: "background.paper" }),
                        width: col.width,
                        whiteSpace: "nowrap",
                      }}
                    >
                      {col.renderCell ? col.renderCell(row) : col.getValue ? col.getValue(row) : (row as Record<string, unknown>)[String(col.id)] as React.ReactNode}
                    </TableCell>
                  ))}
                  <TableCell padding="checkbox" sx={{ position: "sticky", right: 0, zIndex: 1, bgcolor: "background.paper" }}>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 0.5, justifyContent: "flex-end" }}>
                      <IconButton size="small" aria-label="more" onClick={openMenu(row.id)}>
                        <MoreVertIcon fontSize="small" />
                      </IconButton>
                      <IconButton size="small" aria-label="drag handle">
                        <DragIndicatorIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>

      <Menu anchorEl={menuAnchor} open={Boolean(menuAnchor)} onClose={closeMenu} anchorOrigin={{ vertical: "bottom", horizontal: "right" }}>
        <MenuItem onClick={() => { if (menuRowId && onActionClick) onActionClick(rows.find(r => r.id === menuRowId)!, "view"); closeMenu(); }}>View details</MenuItem>
        <MenuItem onClick={() => { if (menuRowId && onActionClick) onActionClick(rows.find(r => r.id === menuRowId)!, "edit"); closeMenu(); }}>Edit</MenuItem>
      </Menu>
    </Paper>
  );
}

export default BaseDataTable;

