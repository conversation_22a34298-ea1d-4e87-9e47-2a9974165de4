import React from "react";
import Button from "@mui/material/Button";
import ButtonGroup from "@mui/material/ButtonGroup";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import SvgIcon, { type SvgIconProps } from "@mui/material/SvgIcon";
import type { CreateScheduleSplitButtonProps } from "@/types/schedule";

const ChevronDownIcon: React.FC<SvgIconProps> = (props) => (
  <SvgIcon {...props} aria-hidden="true" focusable="false" sx={{ fontSize: 16 }}>
    <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6z" />
  </SvgIcon>
);

const DocumentPlusIcon: React.FC<SvgIconProps> = (props) => (
  <SvgIcon {...props} aria-hidden="true" focusable="false" sx={{ fontSize: 16 }}>
    <path
      fill="currentColor"
      d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm-1 9V3.5L18.5 9H13zm0 4h3v2h-3v3h-2v-3H8v-2h3v-3h2v3z"
    />
  </SvgIcon>
);

export const CreateScheduleSplitButton: React.FC<CreateScheduleSplitButtonProps> = ({
  onCreate,
  onSelectTemplate,
  options = [
    { id: "blank", label: "Blank schedule" },
    { id: "from-template", label: "From template…" },
  ],
}) => {
  const menuId = React.useId();
  const menuButtonId = React.useId();

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleToggle = (e: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl((prev) => (prev ? null : e.currentTarget));
  };

  const handleClose = () => setAnchorEl(null);

  const handleCreate = () => {
    onCreate?.();
  };

  const handleSelect = (id: string) => () => {
    onSelectTemplate?.(id);
    handleClose();
  };

  return (
    <>
      <ButtonGroup
        variant="contained"
        aria-label="Create new schedule actions"
        sx={(theme) => ({
          boxShadow: "none",
          borderRadius: "20px",
          overflow: "hidden",
          "& .MuiButtonGroup-grouped:not(:last-of-type)": {
            borderRight: `1px solid ${theme.palette.common.white}26`,
          },
        })}
      >
        <Button
          type="button"
          disableElevation
          onClick={handleCreate}
          startIcon={<DocumentPlusIcon />}
          sx={(theme) => ({
            bgcolor: theme.palette.grey[700],
            color: theme.palette.common.white,
            px: 2.5,
            py: 0.875,
            fontSize: 13,
            fontWeight: 500,
            letterSpacing: "-0.01em",
            textTransform: "none",
            borderRadius: 0,
            "&:hover": {
              bgcolor: theme.palette.grey[800],
            },
          })}
        >
          Create new schedule
        </Button>
        <Button
          type="button"
          disableElevation
          id={menuButtonId}
          aria-label="More create options"
          aria-haspopup="menu"
          aria-controls={open ? menuId : undefined}
          aria-expanded={open ? "true" : undefined}
          onClick={handleToggle}
          sx={(theme) => ({
            bgcolor: theme.palette.grey[700],
            color: theme.palette.common.white,
            minWidth: 36,
            px: 1,
            borderRadius: 0,
            "&:hover": {
              bgcolor: theme.palette.grey[800],
            },
          })}
        >
          <ChevronDownIcon />
        </Button>
      </ButtonGroup>

      <Menu
        id={menuId}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        slotProps={{ list: { "aria-labelledby": menuButtonId } }}
        sx={{
          mt: 0.5,
          "& .MuiPaper-root": {
            borderRadius: 1,
          },
        }}
      >
        {options.map((o) => (
          <MenuItem
            key={o.id}
            onClick={handleSelect(o.id)}
            sx={{ fontSize: 13, py: 0.75 }}
          >
            {o.label}
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};

export default CreateScheduleSplitButton;