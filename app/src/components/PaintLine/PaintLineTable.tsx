import React from "react";
import { BaseDataTable } from "@/components/common/table/BaseDataTable";
import type { Column, Order } from "@/components/common/table/BaseDataTable";

export type PaintLineRow = {
  id: string;
  feedCount: number;
  feedStyle: string;
  order: string | number;
  feedBatch: string;
  uniqueWidth: number;
  slitWidths: string;
  slitPattern: string;
  wastagePct: number;
  hasIssue?: boolean;
};

export type PaintLineTableProps = {
  rows: PaintLineRow[];
  orderBy?: string;
  order?: Order;
  onSortChange?: (orderBy: string, order: Order) => void;
  selected?: string[];
  onSelectionChange?: (ids: string[]) => void;
};

export const PaintLineTable: React.FC<PaintLineTableProps> = ({ rows, orderBy, order, onSortChange, selected, onSelectionChange }) => {
  const columns: Column<PaintLineRow>[] = [
    { id: "feedCount", label: "Feed count", width: 120 },
    { id: "feedStyle", label: "Feed style", width: 140 },
    { id: "order", label: "Order", width: 140, sortable: true },
    { id: "feedBatch", label: "Feed batch", width: 140, sortable: true },
    { id: "uniqueWidth", label: "Unique Width", width: 150, sortable: true },
    { id: "slitWidths", label: "Slit widths", width: 160 },
    { id: "slitPattern", label: "Slit pattern", width: 160 },
    { id: "wastagePct", label: "Wastage", width: 140 },
  ];

  return (
    <BaseDataTable
      columns={columns}
      rows={rows}
      orderBy={orderBy}
      order={order}
      onSortChange={onSortChange}
      selected={selected}
      onSelectionChange={onSelectionChange}
      getRowHighlight={(r) => (r.hasIssue ? "issue" : undefined)}
      height={520}
    />
  );
};

