import React from "react";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import Box from "@mui/material/Box";
import buildingPng from "@/assets/building.png";
import chevronPng from "@/assets/chevron.png";
import type { SelectChangeEvent } from "@mui/material/Select";

export type FacilityOption = { id: string; label: string };

export type FacilitySelectProps = {
  value: string;
  options: FacilityOption[];
  onChange: (value: string) => void;
  ariaLabel?: string;
  minWidth?: number;
};

const BuildingIcon: React.FC = () => (
  <Box
    component="img"
    src={buildingPng}
    alt=""
    aria-hidden
    sx={{ width: 16, height: 16 }}
  />
);

const ChevronIcon: React.FC<React.HTMLAttributes<HTMLSpanElement>> = (props) => (
  <span {...props} aria-hidden>
    <img src={chevronPng} alt="" style={{ width: 16, height: 16 }} />
  </span>
);

export const FacilitySelect: React.FC<FacilitySelectProps> = ({
  value,
  options,
  onChange,
  ariaLabel = "Select facility",
  minWidth = 160,
}) => {
  const handleChange = (e: SelectChangeEvent<string>) => onChange(e.target.value);

  const current = options.find((o) => o.id === value);

  return (
    <FormControl size="small" variant="outlined">
      <Select
        value={value}
        onChange={handleChange}
        IconComponent={ChevronIcon}
        aria-label={ariaLabel}
        renderValue={() => (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <BuildingIcon />
            <Box sx={(theme) => ({ fontSize: 13, fontWeight: 600, color: theme.palette.text.secondary, letterSpacing: "-0.01em" })}>
              {current?.label ?? value}
            </Box>
          </Box>
        )}
        sx={(theme) => ({
          minWidth,
          height: 36,
          bgcolor: theme.palette.background.paper,
          borderRadius: "8px",
          "& .MuiSelect-select": {
            py: 0.75,
            pl: 1.25,
            pr: 3.5,
            display: "flex",
            alignItems: "center",
          },
          "& .MuiSelect-icon": {
            color: theme.palette.grey[500],
            right: 10,
            top: "50%",
            transform: "translateY(-50%)",
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.divider,
            borderWidth: "1px",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.grey[300],
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.grey[400],
          },
        })}
        MenuProps={{
          anchorOrigin: { vertical: "bottom", horizontal: "left" },
          transformOrigin: { vertical: "top", horizontal: "left" },
          PaperProps: {
            sx: {
              mt: 0.5,
              borderRadius: 1,
              "& .MuiMenuItem-root": {
                px: 1.25,
                py: 0.75,
                fontSize: 13,
                "&:hover": { bgcolor: (theme) => theme.palette.grey[100] },
                "&.Mui-selected": {
                  bgcolor: "transparent",
                  "&:hover": { bgcolor: (theme) => theme.palette.grey[100] },
                },
              },
            },
          },
        }}
      >
        {options.map((opt) => (
          <MenuItem key={opt.id} value={opt.id} sx={{ fontSize: 13 }}>
            {opt.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default FacilitySelect;