import React from "react";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import { useLocation, useNavigate } from "react-router-dom";
import type { PrimaryNavTabsProps } from "@/types/header";

const TABS = [
  { label: "Dashboard", subPath: "" },
  { label: "Optimise", subPath: "optimise" },
] as const;

export const PrimaryNavTabs: React.FC<PrimaryNavTabsProps> = ({ basePath }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const baseNoTrailing = React.useMemo(
    () => basePath.replace(/\/+$/, ""),
    [basePath]
  );

  const currentIndex = React.useMemo(() => {
    const pathname = location.pathname;

    let rel = pathname;
    if (baseNoTrailing) {
      const absBase = baseNoTrailing.startsWith("/")
        ? baseNoTrailing
        : `/${baseNoTrailing}`;
      if (rel.startsWith(absBase)) {
        rel = rel.slice(absBase.length);
      } else if (rel.startsWith(baseNoTrailing)) {
        rel = rel.slice(baseNoTrailing.length);
      }
    }
    rel = rel.replace(/^\/+/, "");

    const firstSegment = rel.split("/")[0] || "";
    const idx = TABS.findIndex((t) => t.subPath === firstSegment);
    return idx === -1 ? 0 : idx;
  }, [location.pathname, baseNoTrailing]);

  const handleChange = (_: React.SyntheticEvent, index: number) => {
    const tab = TABS[index];
    const path = tab.subPath
      ? `${baseNoTrailing}/${tab.subPath}`
      : baseNoTrailing;

    const targetAbsolute = path.startsWith("/")
      ? path
      : new URL(
          path,
          window.location.origin +
            (location.pathname.endsWith("/")
              ? location.pathname
              : `${location.pathname}/`)
        ).pathname;

    if (targetAbsolute !== location.pathname) {
      navigate(path);
    }
  };

  return (
    <Tabs
      value={currentIndex}
      onChange={handleChange}
      aria-label="Primary navigation"
      textColor="primary"
      indicatorColor="primary"
      slotProps={{
        indicator: {
          sx: { display: "none" },
        },
      }}
      sx={(theme) => ({
        minHeight: 32,
        "& .MuiTabs-flexContainer": {
          height: 32,
          gap: 1,
        },
        "& .MuiTab-root": {
          minHeight: 32,
          minWidth: "auto",
          py: 0.5,
          px: 1.5,
          mx: 0.5,
          borderRadius: "6px",
          textTransform: "none",
          fontSize: 13,
          fontWeight: 500,
          letterSpacing: "-0.01em",
          color: theme.palette.grey[500],
          transition: "all 0.2s",
          "&:hover": {
            bgcolor: theme.palette.grey[100],
            color: theme.palette.grey[700],
          },
          "&.Mui-selected": {
            color: theme.palette.grey[900],
            fontWeight: 700,
            bgcolor: "transparent",
            "&:hover": {
              bgcolor: theme.palette.grey[100],
            },
          },
        },
      })}
    >
      {TABS.map((t) => (
        <Tab key={t.label} label={t.label} disableRipple />
      ))}
    </Tabs>
  );
};

export default PrimaryNavTabs;