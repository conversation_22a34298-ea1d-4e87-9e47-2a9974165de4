import React from "react";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import Box from "@mui/material/Box";
import { useTheme } from "@mui/material/styles";
import type { Theme } from "@mui/material/styles";
import chevronPng from "@/assets/chevron.png";
import type { SelectChangeEvent } from "@mui/material/Select";
import type { SelectionSwitcherProps } from "@/types/header";
type ThemeColorToken = string | undefined;

function resolveColorToken(token: ThemeColorToken, theme: Theme): string | undefined {
  if (!token) return undefined;
  
  if (token.startsWith("gradients.")) {
    const key = token.split(".")[1] as keyof Theme["gradients"];
    return theme.gradients?.[key];
  }
  
  if (token.includes(".")) {
    const [group, shade] = token.split(".");
    const paletteAny = theme.palette as unknown as Record<string, unknown>;
    const groupObj = paletteAny[group] as Record<string, unknown> | undefined;
    if (groupObj && Object.prototype.hasOwnProperty.call(groupObj, shade)) {
      return String(groupObj[shade]);
    }
  }
  return token;
}


const DOT_SIZE = 12;

const ColorDot: React.FC<{ color?: string }> = ({ color }) => {
  const isGradient = typeof color === "string" && /(repeating-)?(linear|radial)-gradient\(/i.test(color);
  return (
    <Box
      aria-hidden
      sx={(theme) => ({
        width: DOT_SIZE,
        height: DOT_SIZE,
        borderRadius: "50%",
        flexShrink: 0,
        boxShadow: "0 0 0 1px rgba(0,0,0,0.06)",
        ...(isGradient ? { background: color } : { bgcolor: color || theme.palette.grey[300] }),
      })}
    />
  );
};

type SelectIconProps = { className?: string };
const DoubleChevronIcon: React.FC<SelectIconProps> = ({ className }) => (
  <Box
    className={className}
    sx={{ display: "flex", flexDirection: "column", gap: "1px", pointerEvents: "none" }}
    role="presentation"
  >
    <Box
      component="img"
      src={chevronPng}
      alt=""
      aria-hidden
      sx={{ width: 12, height: 8, display: "block", transform: "rotate(180deg)" }}
  />
  </Box>
);

export const SelectionSwitcher: React.FC<SelectionSwitcherProps> = ({
  options,
  value,
  onChange,
  size = "small",
  minWidth = 130,
  "aria-label": ariaLabel = "Select section",
}) => {
  const handleChange = (e: SelectChangeEvent<string>) => {
    onChange(e.target.value);
  };

  const theme = useTheme();
  const current = options.find((o) => o.value === value);
  const isSmall = size === "small";

  return (
    <FormControl size={size} variant="outlined">
      <Select
        value={value}
        onChange={handleChange}
        aria-label={ariaLabel}
        displayEmpty={false}
        IconComponent={DoubleChevronIcon}
        renderValue={() => (
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <ColorDot color={resolveColorToken(current?.color, theme)} />
            <Box sx={(theme) => ({ fontSize: 13, fontWeight: 600, color: theme.palette.text.secondary, letterSpacing: "-0.01em" })}>
              {current?.label ?? value}
            </Box>
          </Box>
        )}
        sx={(theme) => ({
          minWidth,
          height: isSmall ? 32 : 40,
          bgcolor: theme.palette.background.paper,
          borderRadius: "6px",
          "& .MuiSelect-select": {
            py: isSmall ? 0.75 : 1,
            pl: 1.25,
            pr: 3.5,
            display: "flex",
            alignItems: "center",
          },
          "& .MuiSelect-icon": {
            color: theme.palette.grey[500],
            right: 10,
            top: "50%",
            transform: "translateY(-50%)",
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.divider,
            borderWidth: "1px",
          },
          "&:hover .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.grey[300],
            borderWidth: "1px",
          },
          "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            borderColor: theme.palette.grey[400],
            borderWidth: "1px",
          },
        })}
        MenuProps={{
          anchorOrigin: { vertical: "bottom", horizontal: "left" },
          transformOrigin: { vertical: "top", horizontal: "left" },
          PaperProps: {
            sx: {
              mt: 0.5,
              borderRadius: 1,
              "& .MuiMenuItem-root": {
                px: 1.25,
                py: 0.75,
                fontSize: 13,
                "&:hover": { bgcolor: (theme) => theme.palette.grey[100] },
                "&.Mui-selected": {
                  bgcolor: "transparent",
                  "&:hover": { bgcolor: (theme) => theme.palette.grey[100] },
                },
              },
            },
          },
        }}
      >
        {options.map((opt) => {
          const isSelected = opt.value === value;
          return (
            <MenuItem key={opt.value} value={opt.value}>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <ColorDot color={resolveColorToken(opt.color, theme)} />
                <Box
                  component="span"
                  sx={(theme) => ({
                    fontSize: 13,
                    fontWeight: isSelected ? 700 : 500,
                    color: isSelected ? theme.palette.grey[900] : theme.palette.grey[700],
                    letterSpacing: "-0.01em",
                  })}
                >
                  {opt.label}
                </Box>
              </Box>
            </MenuItem>
          );
        })}
      </Select>
    </FormControl>
  );
};

export default SelectionSwitcher;