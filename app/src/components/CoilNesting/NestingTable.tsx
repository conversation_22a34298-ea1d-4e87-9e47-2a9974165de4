import React from "react";
import Chip from "@mui/material/Chip";
import Stack from "@mui/material/Stack";
import Typography from "@mui/material/Typography";
import { BaseDataTable } from "@/components/common/table/BaseDataTable";
import type { Column, Order } from "@/components/common/table/BaseDataTable";
import { WarningAmberIcon } from "@/components/common/icons";

export type NestingRow = {
  id: string;
  feedCount: number;
  feedStyle: string;
  order: string | number;
  feedBatch: string;
  uniqueWidth: number;
  slitWidths: string; // display-ready string for simplicity
  slitPattern: string;
  wastagePct: number;
  hasIssue?: boolean;
};

export type NestingTableProps = {
  rows: NestingRow[];
  orderBy?: string;
  order?: Order;
  onSortChange?: (orderBy: string, order: Order) => void;
  selected?: string[];
  onSelectionChange?: (ids: string[]) => void;
};

export const NestingTable: React.FC<NestingTableProps> = ({ rows, orderBy, order, onSortChange, selected, onSelectionChange }) => {
  const columns: Column<NestingRow>[] = [
    { id: "feedCount", label: "Feed count", width: 120, sortable: false },
    { id: "feedStyle", label: "Feed style", width: 140, sortable: false },
    { id: "order", label: "Order", width: 140, sortable: true },
    { id: "feedBatch", label: "Feed batch", width: 140, sortable: true },
    { id: "uniqueWidth", label: "Unique Width", width: 150, sortable: true },
    { id: "slitWidths", label: "Slit widths", width: 160 },
    { id: "slitPattern", label: "Slit pattern", width: 160 },
    {
      id: "wastagePct",
      label: "Wastage",
      width: 140,
      renderCell: (r) => (
        <Stack direction="row" spacing={1} alignItems="center">
          <Typography variant="body2" component="span">{r.wastagePct.toFixed(1)}%</Typography>
          {r.hasIssue && (
            <Chip
              size="small"
              icon={<WarningAmberIcon sx={{ color: (t) => t.palette.customLightOrange.contrastText }} />}
              label=""
              sx={{
                height: 20,
                bgcolor: (t) => t.palette.customLightOrange.main,
                color: (t) => t.palette.customLightOrange.contrastText,
                px: 0.5,
                "& .MuiChip-icon": { mr: 0 },
              }}
            />
          )}
        </Stack>
      ),
    },
  ];

  return (
    <BaseDataTable
      columns={columns}
      rows={rows}
      orderBy={orderBy}
      order={order}
      onSortChange={onSortChange}
      selected={selected}
      onSelectionChange={onSelectionChange}
      getRowHighlight={(r) => (r.hasIssue ? "issue" : undefined)}
      height={520}
    />
  );
};

