import React from "react";
import Box from "@mui/material/Box";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import { useLocation, useNavigate } from "react-router-dom";

export type SecondaryNavTabsProps = {
  basePath: string; 
};

const TABS = [
  { label: "Dashboard", subPath: "" },
  { label: "Past schedules", subPath: "past-schedules" },
  { label: "Configuration", subPath: "configuration" },
  { label: "OD", subPath: "od" },
] as const;

export const SecondaryNavTabs: React.FC<SecondaryNavTabsProps> = ({ basePath }) => {
  const location = useLocation();
  const navigate = useNavigate();

  const baseNoTrailing = React.useMemo(() => basePath.replace(/\/+$/, ""), [basePath]);

  const currentIndex = React.useMemo(() => {
    let rel = location.pathname;
    const absBase = baseNoTrailing.startsWith("/") ? baseNoTrailing : `/${baseNoTrailing}`;
    if (rel.startsWith(absBase)) {
      rel = rel.slice(absBase.length);
    } else if (rel.startsWith(baseNoTrailing)) {
      rel = rel.slice(baseNoTrailing.length);
    }
    rel = rel.replace(/^\/+/, "");

    const first = rel.split("/")[0] || "";
    const idx = TABS.findIndex((t) => t.subPath === first);
    return idx === -1 ? 0 : idx;
  }, [location.pathname, baseNoTrailing]);

  const handleChange = (_: React.SyntheticEvent, index: number) => {
    const tab = TABS[index];
    const path = tab.subPath ? `${baseNoTrailing}/${tab.subPath}` : baseNoTrailing;
    if (path !== location.pathname) {
      navigate(path);
    }
  };

  return (
    <Box sx={{ width: "max-content" }}>
      <Tabs
        value={currentIndex}
        onChange={handleChange}
        aria-label="Coil Nesting secondary navigation"
        variant="scrollable"
        scrollButtons="auto"
        textColor="primary"
        indicatorColor="primary"
        slotProps={{ indicator: { sx: { display: "none" } } }}
        sx={(theme) => ({
          mt: 2,
          p: 0.5,
          minHeight: 36,
          bgcolor: theme.palette.background.paper,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: "8px",
          "& .MuiTabs-flexContainer": { height: 36, gap: 2 },
          "& .MuiTab-root": {
            minHeight: 32,
            minWidth: "auto",
            px: 1.25,
            mx: 0,
            borderRadius: "6px",
            textTransform: "none",
            fontSize: 13,
            fontWeight: 500,
            letterSpacing: "-0.01em",
            color: theme.palette.grey[700],
            transition: "background-color 0.2s ease",
            "&:hover": { bgcolor: theme.palette.grey[50] || theme.palette.grey[100] },
            "&.Mui-selected": {
              color: theme.palette.grey[900],
              fontWeight: 700,
              bgcolor: theme.palette.grey[200],
            },
          },
        })}
      >
        {TABS.map((t) => (
          <Tab key={t.label} label={t.label} disableRipple />
        ))}
      </Tabs>
    </Box>
  );
};

export default SecondaryNavTabs;