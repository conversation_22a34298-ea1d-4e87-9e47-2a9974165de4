import type { Components, Theme } from '@mui/material/styles';

export const components: Components<Omit<Theme, 'components'>> = {
  MuiButton: {
    defaultProps: {
      disableElevation: true,
    },
    styleOverrides: {
      root: ({ theme }) => ({
        borderRadius: 8,
        padding: theme.spacing(1, 2.75),
      }),
      containedPrimary: ({ theme }) => ({
        '&:hover': {
          backgroundColor: theme.palette.primary.dark,
        },
      }),
    },
  },
  MuiAppBar: {
    defaultProps: {
      elevation: 0,
      color: 'default',
    },
    styleOverrides: {
      root: ({ theme }) => ({
        borderBottom: `1px solid ${theme.palette.divider}`,
        backgroundColor: theme.palette.background.paper,
        color: theme.palette.text.primary,
      }),
    },
  },
  MuiCard: {
    defaultProps: { elevation: 0 },
    styleOverrides: {
      root: ({ theme }) => ({ borderRadius: 12, border: `1px solid ${theme.palette.divider}` }),
    },
  },
  MuiTable: { defaultProps: { size: 'small', stickyHeader: true } },
  MuiTableCell: {
    styleOverrides: {
      root: ({ theme }) => ({ paddingTop: 10, paddingBottom: 10, borderColor: theme.palette.divider }),
      head: ({ theme }) => ({ fontWeight: 600, color: theme.palette.grey[700], backgroundColor: theme.palette.background.paper }),
    },
  },
  MuiCheckbox: { defaultProps: { size: 'small' } },
  MuiIconButton: { defaultProps: { size: 'small' } },
};
