import type { PaletteOptions } from '@mui/material/styles';

declare module '@mui/material/styles' {
  interface Palette {
    customLightBlue: Palette['primary'];
    customLightRed: Palette['primary'];
    customLightOrange: Palette['primary'];
    customLightGreen: Palette['primary'];
    customSignal: Palette['primary'];
  }

  interface PaletteOptions {
    customLightBlue?: PaletteOptions['primary'];
    customLightRed?: PaletteOptions['primary'];
    customLightOrange?: PaletteOptions['primary'];
    customLightGreen?: PaletteOptions['primary'];
    customSignal?: PaletteOptions['primary'];
  }

  interface SimplePaletteColorOptions {
    contrast?: string;
  }

  interface PaletteColor {
    contrast?: string;
  }
}

declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides {
    customLightBlue: true;
    customLightRed: true;
    customLightOrange: true;
    customLightGreen: true;
    customSignal: true;
  }
}

export const palette: PaletteOptions = {
  primary: {
    main: '#0044B4',
    contrastText: '#FFFFFF',
  },
  secondary: {
    main: '#002E7C',
    contrastText: '#FFFFFF',
  },
  customLightBlue: {
    main: '#1ED2E6',
    contrast: '#00B4D4',
    contrastText: '#000000',
  },
  customLightRed: {
    main: '#FF5A54',
    contrast: '#F05252',
    contrastText: '#FFFFFF',
  },
  customLightOrange: {
    main: '#FF9900',
    contrast: '#EE8700',
    contrastText: '#FFFFFF',
  },
  customLightGreen: {
    main: '#00E191',
    contrast: '#0AC84E',
    contrastText: '#FFFFFF',
  },
  customSignal: {
    main: '#E6FF00',
    contrastText: '#000000',
  },
  background: {
    default: '#F4F6F8',
    paper: '#FFFFFF',
  },
  divider: '#E5E7EB',
  grey: {
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },
  text: {
    primary: '#1A2027',
    secondary: '#637381',
  },
};