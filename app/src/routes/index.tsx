import React, { lazy, Suspense } from "react";
import {
  create<PERSON>rowser<PERSON>outer,
  RouterProvider,
  Navigate,
  useRouteError,
} from "react-router-dom";

import { LoginScreen } from "@/layouts/LoginScreen";
import Box from "@mui/material/Box";
import Container from "@mui/material/Container";
import Typography from "@mui/material/Typography";
import Alert from "@mui/material/Alert";
import CircularProgress from "@mui/material/CircularProgress";
import { ProtectedLayout } from "@/layouts/ProtectedLayout";
import { PublicLayout } from "@/layouts/PublicLayout";
import { ROUTES } from "@/types/routes";

const CoilNesting = lazy(() =>
  import("@/pages/CoilNesting").then((m) => ({ default: m.CoilNesting }))
);
const PaintLine = lazy(() =>
  import("@/pages/PaintLine").then((m) => ({ default: m.PaintLine }))
);

const CoilNestingOptimise = lazy(() =>
  import("@/pages/optimise/CoilNestingOptimise").then((m) => ({
    default: m.CoilNestingOptimise,
  }))
);
const PaintLineOptimise = lazy(() =>
  import("@/pages/optimise/PaintLineOptimise").then((m) => ({
    default: m.PaintLineOptimise,
  }))
);

const FullPageLoader: React.FC = () => (
  <Box sx={{ minHeight: "100vh", display: "grid", placeItems: "center" }}>
    <CircularProgress aria-label="Loading" />
  </Box>
);



const NotFound: React.FC = () => (
  <Container sx={{ py: 3 }}>
    <Typography variant="h5" fontWeight={600}>
      404
    </Typography>
    <Typography color="text.secondary">Page not found.</Typography>
  </Container>
);

const RouteErrorBoundary: React.FC = () => {
  const error = useRouteError();
  console.error(error);
  return (
    <Container sx={{ py: 3 }}>
      <Alert severity="error" variant="outlined">
        <Typography variant="h6" gutterBottom>
          Something went wrong
        </Typography>
        <Typography>Try reloading the page.</Typography>
      </Alert>
    </Container>
  );
};

const router = createBrowserRouter([
  {
    element: <PublicLayout />,
    errorElement: <RouteErrorBoundary />,
    children: [{ path: ROUTES.LOGIN_PATH, element: <LoginScreen /> }],
  },
  {
    element: <ProtectedLayout />,
    errorElement: <RouteErrorBoundary />,
    children: [
      { index: true, element: <Navigate to={ROUTES.COIL_NESTING} replace /> },
      { path: ROUTES.COIL_NESTING, element: <CoilNesting /> },
      { path: `${ROUTES.COIL_NESTING}/past-schedules`, element: <CoilNesting /> },
      { path: `${ROUTES.COIL_NESTING}/configuration`, element: <CoilNesting /> },
      { path: `${ROUTES.COIL_NESTING}/od`, element: <CoilNesting /> },
      { path: ROUTES.COIL_NESTING_OPTIMISE, element: <CoilNestingOptimise /> },
      { path: ROUTES.PAINT_LINE, element: <PaintLine /> },
      { path: ROUTES.PAINT_LINE_OPTIMISE, element: <PaintLineOptimise /> },
    ],
  },
  {
    path: "*",
    element: <NotFound />,
  },
]);

export const AppRouter: React.FC = () => (
  <Suspense fallback={<FullPageLoader />}>
    <RouterProvider router={router} />
  </Suspense>
);