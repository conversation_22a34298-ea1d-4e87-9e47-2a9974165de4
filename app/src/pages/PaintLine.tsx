import React from "react";
import Container from "@mui/material/Container";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import { CreateScheduleSplitButton } from "@/components/Schedules/CreateScheduleSplitButton";
import { PaintLineTable, type PaintLineRow } from "@/components/PaintLine/PaintLineTable";

export const PaintLine: React.FC = () => {
  const [selected, setSelected] = React.useState<string[]>([]);
  const [orderBy, setOrderBy] = React.useState<string | undefined>("order");
  const [order, setOrder] = React.useState<"asc" | "desc">("asc");
  const rows = React.useMemo<PaintLineRow[]>(() => [
    { id: "pl1", feedCount: 2, feedStyle: "X", order: "PL-1001", feedBatch: "X-1", uniqueWidth: 1200, slitWidths: "200,200", slitPattern: "XX", wastagePct: 3.1 },
    { id: "pl2", feedCount: 1, feedStyle: "Y", order: "PL-1002", feedBatch: "Y-2", uniqueWidth: 1185, slitWidths: "100,150,150", slitPattern: "YZZ", wastagePct: 7.4, hasIssue: true },
  ], []);

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 4 }}>
        <Typography
          variant="h4"
          sx={{
            fontSize: 28,
            fontWeight: 700,
            color: "#111827",
            letterSpacing: "-0.02em",
          }}
        >
          Welcome back!
        </Typography>
        <CreateScheduleSplitButton />
      </Box>

      <PaintLineTable
        rows={rows}
        selected={selected}
        onSelectionChange={setSelected}
        orderBy={orderBy}
        order={order}
        onSortChange={(by, ord) => { setOrderBy(by); setOrder(ord); }}
      />
    </Container>
  );
};