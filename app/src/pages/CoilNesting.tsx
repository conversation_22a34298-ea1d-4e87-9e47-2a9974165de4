import React from "react";
import Container from "@mui/material/Container";
import Typography from "@mui/material/Typography";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Paper from "@mui/material/Paper";
import { CreateScheduleSplitButton } from "@/components/Schedules/CreateScheduleSplitButton";
import { FacilitySelect } from "@/components/Facility/FacilitySelect";
import { SecondaryNavTabs } from "@/components/CoilNesting/SecondaryNavTabs";
import { ROUTES } from "@/types/routes";
import { NestingTable, type NestingRow } from "@/components/CoilNesting/NestingTable";

export const CoilNesting: React.FC = () => {
  const [facility, setFacility] = React.useState("grandville");
  const [selected, setSelected] = React.useState<string[]>([]);
  const [orderBy, setOrderBy] = React.useState<string | undefined>("order");
  const [order, setOrder] = React.useState<"asc" | "desc">("asc");

  const rows = React.useMemo<NestingRow[]>(() => [
    { id: "1", feedCount: 2, feedStyle: "A", order: "12345", feedBatch: "B-11", uniqueWidth: 1200, slitWidths: "150,150,200", slitPattern: "AAB", wastagePct: 2.5, hasIssue: false },
    { id: "2", feedCount: 3, feedStyle: "B", order: "12346", feedBatch: "B-12", uniqueWidth: 1180, slitWidths: "100,200,200", slitPattern: "ABC", wastagePct: 6.2, hasIssue: true },
    { id: "3", feedCount: 1, feedStyle: "C", order: "12347", feedBatch: "C-21", uniqueWidth: 1190, slitWidths: "100,100,100", slitPattern: "CCC", wastagePct: 1.1 },
  ], []);

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 2 }}>
        <Typography
          variant="h4"
          sx={{
            fontSize: 28,
            fontWeight: 700,
            color: "grey.900",
            letterSpacing: "-0.02em",
          }}
        >
          Welcome back!
        </Typography>
        <CreateScheduleSplitButton />
      </Box>

      <Box sx={{ mb: 2 }}>
        <FacilitySelect
          value={facility}
          onChange={setFacility}
          options={[{ id: "grandville", label: "Grandville" }]}
        />
      </Box>

      <SecondaryNavTabs basePath={ROUTES.COIL_NESTING} />

      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mt: 3,
          mb: 1.5,
        }}
      >
        <Typography
          variant="h6"
          sx={{ fontSize: 16, fontWeight: 700, color: "grey.900", letterSpacing: "-0.01em" }}
        >
          Summary of most recent optimisation
        </Typography>
        <Button variant="outlined" size="small" sx={{ textTransform: "none", borderRadius: "8px" }}>
          View full schedule
        </Button>
      </Box>

      <Paper variant="outlined" sx={{ bgcolor: "grey.100", borderColor: "divider", borderRadius: 2, height: 180 }} />

      <Box sx={{ mt: 3 }}>
        <NestingTable
          rows={rows}
          selected={selected}
          onSelectionChange={setSelected}
          orderBy={orderBy}
          order={order}
          onSortChange={(by, ord) => { setOrderBy(by); setOrder(ord); }}
        />
      </Box>
    </Container>
  );
};