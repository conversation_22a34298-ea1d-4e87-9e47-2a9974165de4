// Azure AD authentication context and types
import { createContext } from "react";
import type { AccountInfo } from "@azure/msal-browser";

export interface AuthContextType {
  isAuthenticated: boolean;
  user: AccountInfo | null;
  /** Best-effort human-readable display name, derived from account or ID token claims */
  displayName: string | null;
  login: () => Promise<void>;
  logout: () => Promise<void>;
  acquireToken: () => Promise<string | null>;
  loading: boolean;
  error: string | null;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);
