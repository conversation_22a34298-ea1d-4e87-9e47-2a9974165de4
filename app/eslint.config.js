import js from "@eslint/js";
import globals from "globals";
import reactHooks from "eslint-plugin-react-hooks";
import reactRefresh from "eslint-plugin-react-refresh";
import react from "eslint-plugin-react";
import jsxA11y from "eslint-plugin-jsx-a11y";
import tseslint from "typescript-eslint";
import eslintConfigPrettier from "eslint-config-prettier/flat";

export default tseslint.config([
  {
    ignores: ["dist"],
  },
  {
    files: ["**/*.{ts,tsx}"],
    extends: [
      js.configs.recommended,
      ...tseslint.configs.recommended,
      react.configs.flat.recommended,
      react.configs.flat["jsx-runtime"],
      reactHooks.configs["recommended-latest"],
      jsxA11y.flatConfigs.recommended,
      reactRefresh.configs.vite,
      eslintConfigPrettier,
    ],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    settings: {
      react: {
        version: "detect",
      },
    },
  },
  // Minimal guardrail: forbid color literals in components; use theme tokens instead
  {
    files: ["src/components/**/*.{ts,tsx}"],
    rules: {
      // Disallow hex color literals like "#FFFFFF"
      "no-restricted-syntax": [
        "error",
        {
          selector: "Literal[value=/^#(?:[0-9a-fA-F]{3}){1,2}$/]",
          message: "Use theme tokens (e.g., theme.palette.*) instead of hex colors in components.",
        },
        // Disallow rgb/rgba/hsl/hsla CSS functions
        {
          selector: "Literal[value=/^(?:rgb|hsl)a?\\(/]",
          message: "Use theme tokens instead of rgb()/hsl() colors in components.",
        },
        // Disallow gradient strings
        {
          selector: "Literal[value=/gradient\\(/i]",
          message: "Use theme tokens (e.g., theme.gradients.*) instead of gradient strings in components.",
        },
      ],
    },
  },
]);
